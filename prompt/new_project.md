
### 要求
- 代码覆盖率需达到80%及以上，测试通过率要达到95%及以上，不得因为覆盖率而增加测试用例，应考虑参数问题
- 对于 `$this->expectExceptionCode()`，若参数取常量，需使用常量数组中的第0个值。
- 验证异常消息时，应使用 `$this->expectExceptionMessage()` 方法。
- 不应在 `tearDown()` 方法中手动删除数据，应依赖事务回滚机制
- 不适用Mockery::mock方法
- 参数设置读取/protmpt/parmas/目录下，如测试 `CoinLogic.php` 则读取 `protmpt/parmas/coin/CoinLogic.md`,读取不到忽略


### 测试文件位置
    - Logic层测试：`tests/Feature/Logic/{类名}Test.php`
    - Service层测试：`tests/Feature/Service/{类名}Test.php`
    - Controller层测试：`tests/Feature/Controller/{类名}Test.php`
    - Model层测试：`tests/Feature/Model/{类名}Test.php`

### 数据插入
- 使用 `{xxxModel}::query()->insert($data)` 插入数据
- 获取ID时使用 `{xxxModel}::query()->insertGetId($data)`
- 不手动指定ID，使用自增ID

1. **边界条件测试**：验证系统对极限或临界值的处理，包括：
    - 数值边界（最大值、最小值、零值）
    - 集合边界（空集合、单元素集合、最大容量集合）
    - 时间边界（日期变更点、时区转换点）
    - 资源边界（内存限制、连接数限制）
2. **异常流程测试**：验证系统对异常情况的处理，包括：
    - 无效参数处理
    - 缺失参数处理
    - 参数类型错误处理
    - 数据不存在情况
    - 权限错误情况
    - 资源不可用情况
3. **正常流程测试**：验证常规业务流程的正确性，包括：
    - 数据创建成功
    - 数据更新成功
    - 数据删除成功
    - 数据查询正确

