<?php


namespace App\Models;

/**
 * 业务字段
 * Class BusinessFieldModel
 * @package App\Models
 *
 * @property int $id
 * @property int $business_id 业务ID
 * @property string $name 名称
 * @property string $field_type 字段类型
 * @property string $field_key 字段key
 * @property string $placeholder 字段提示
 * @property int $max_length 最大长度
 * @property string $biz_type 业务类型 输入input 输出output
 * @property int $is_required 是否必填 0否 1是
 * @property int $state 状态 0禁用 1启用
 * @property int $is_deleted 是否删除 0否 1是
 * @property string $create_time 创建时间
 * @property string $modify_time 更新时间
 */
class BusinessFieldModel extends BaseModel
{
    protected $table = 'business_field';

    public $timestamps = false;
}
